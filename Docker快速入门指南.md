# 🐳 Docker快速入门指南

---

## 📚 目录

- [1. Docker简介与核心概念](#1-docker简介与核心概念)
- [2. 环境安装与配置](#2-环境安装与配置)
- [3. Docker核心命令详解](#3-docker核心命令详解)
- [4. Dockerfile编写指南](#4-dockerfile编写指南)
- [5. 实践练习项目](#5-实践练习项目)
- [6. 常见问题与故障排除](#6-常见问题与故障排除)
- [7. 学习检查清单](#7-学习检查清单)
- [8. 进阶学习路径](#8-进阶学习路径)

---

## 1. Docker简介

### 🎯 什么是Docker？

Docker是一个开源的容器化平台，它允许开发者将应用程序及其依赖项打包到一个轻量级、可移植的容器中，然后可以在任何支持Docker的环境中运行。

### 🔄 容器 vs 虚拟机对比

| 特性 | 容器（Container） | 虚拟机（Virtual Machine） |
|------|------------------|--------------------------|
| **资源占用** | 轻量级，共享主机内核 | 重量级，需要完整操作系统 |
| **启动速度** | 秒级启动 | 分钟级启动 |
| **性能** | 接近原生性能 | 有一定性能损耗 |
| **隔离性** | 进程级隔离 | 硬件级隔离 |
| **可移植性** | 高度可移植 | 相对较低 |

```bash
# 容器架构示意
┌─────────────────────────────────────┐
│           应用程序层                 │
├─────────────────────────────────────┤
│    Container1  │  Container2        │
│   ┌─────────┐  │  ┌─────────┐      │
│   │  App A  │  │  │  App B  │      │
│   │ Runtime │  │  │ Runtime │      │
│   └─────────┘  │  └─────────┘      │
├─────────────────────────────────────┤
│         Docker Engine               │
├─────────────────────────────────────┤
│         Host Operating System       │
└─────────────────────────────────────┘
```

### 🏗️ Docker核心概念

#### 1. 镜像（Image）
- **定义**：只读的模板，用于创建容器
- **特点**：分层存储，支持版本控制
- **类比**：就像程序的安装包

```bash
# 镜像层级结构示例
ubuntu:20.04
├── Layer 1: 基础Ubuntu系统
├── Layer 2: 安装Python
├── Layer 3: 安装应用依赖
└── Layer 4: 复制应用代码
```

#### 2. 容器（Container）
- **定义**：镜像的运行实例
- **特点**：可读写，独立的运行环境
- **类比**：就像正在运行的程序

#### 3. 仓库（Repository）
- **Docker Hub**：官方公共仓库
- **私有仓库**：企业内部使用
- **本地仓库**：开发测试环境

---


## 3. Docker核心命令详解

### 📋 镜像管理命令

#### 1. `docker images` - 查看本地镜像

```bash
# 语法
docker images [OPTIONS] [REPOSITORY[:TAG]]

# 常用参数
-a, --all         # 显示所有镜像（包括中间层）
-q, --quiet       # 只显示镜像ID
--format          # 格式化输出

# 使用示例
docker images                    # 查看所有本地镜像
docker images ubuntu            # 查看ubuntu相关镜像
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"

# 使用场景
# - 检查本地有哪些镜像
# - 查看镜像大小和版本
# - 清理不需要的镜像前的确认
```

#### 2. `docker pull` - 拉取镜像

```bash
# 语法
docker pull [OPTIONS] NAME[:TAG|@DIGEST]

# 使用示例
docker pull ubuntu              # 拉取最新版ubuntu
docker pull ubuntu:20.04       # 拉取指定版本
docker pull nginx:alpine       # 拉取轻量版nginx

# 使用场景
# - 获取基础镜像用于构建
# - 更新镜像到最新版本
# - 预先下载需要的镜像
```

#### 3. `docker rmi` - 删除镜像

```bash
# 语法
docker rmi [OPTIONS] IMAGE [IMAGE...]

# 常用参数
-f, --force       # 强制删除

# 使用示例
docker rmi ubuntu:20.04         # 删除指定镜像
docker rmi $(docker images -q)  # 删除所有镜像
docker rmi -f image_id          # 强制删除

# 使用场景
# - 清理不需要的镜像释放空间
# - 删除构建失败的镜像
```

### 🚀 容器管理命令

#### 4. `docker run` - 创建并运行容器

```bash
# 语法
docker run [OPTIONS] IMAGE [COMMAND] [ARG...]

# 常用参数
-d, --detach          # 后台运行
-it                   # 交互式运行
-p, --publish         # 端口映射
-v, --volume          # 数据卷挂载
--name                # 指定容器名称
--rm                  # 容器停止后自动删除
-e, --env             # 设置环境变量

# 使用示例
docker run hello-world                    # 运行测试容器
docker run -it ubuntu bash              # 交互式运行ubuntu
docker run -d --name mynginx -p 8080:80 nginx  # 后台运行nginx并映射端口
docker run --rm -v $(pwd):/app ubuntu ls /app  # 挂载当前目录并执行命令

# 使用场景
# - 快速测试应用
# - 启动服务容器
# - 开发环境搭建
```

#### 5. `docker ps` - 查看容器状态

```bash
# 语法
docker ps [OPTIONS]

# 常用参数
-a, --all         # 显示所有容器（包括停止的）
-q, --quiet       # 只显示容器ID
-l, --latest      # 显示最新创建的容器
--format          # 格式化输出

# 使用示例
docker ps                     # 查看运行中的容器
docker ps -a                  # 查看所有容器
docker ps -q                  # 只显示容器ID
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# 使用场景
# - 检查容器运行状态
# - 获取容器ID用于其他操作
# - 监控容器资源使用
```

#### 6. `docker exec` - 在运行容器中执行命令

```bash
# 语法
docker exec [OPTIONS] CONTAINER COMMAND [ARG...]

# 常用参数
-it               # 交互式执行
-d, --detach      # 后台执行
-u, --user        # 指定用户

# 使用示例
docker exec -it mynginx bash           # 进入nginx容器的bash
docker exec mynginx ls /etc/nginx      # 在容器中执行ls命令
docker exec -u root mynginx whoami     # 以root用户执行命令

# 使用场景
# - 调试运行中的容器
# - 在容器中执行维护任务
# - 查看容器内部状态
```

#### 7. `docker stop/start/restart` - 容器生命周期管理

```bash
# 停止容器
docker stop [OPTIONS] CONTAINER [CONTAINER...]
docker stop mynginx                    # 优雅停止
docker stop -t 30 mynginx             # 30秒后强制停止

# 启动容器
docker start [OPTIONS] CONTAINER [CONTAINER...]
docker start mynginx                   # 启动已停止的容器

# 重启容器
docker restart [OPTIONS] CONTAINER [CONTAINER...]
docker restart mynginx                 # 重启容器

# 使用场景
# - 服务维护和更新
# - 故障恢复
# - 资源管理
```

#### 8. `docker rm` - 删除容器

```bash
# 语法
docker rm [OPTIONS] CONTAINER [CONTAINER...]

# 常用参数
-f, --force       # 强制删除运行中的容器
-v, --volumes     # 删除关联的数据卷

# 使用示例
docker rm mynginx                      # 删除停止的容器
docker rm -f mynginx                   # 强制删除运行中的容器
docker rm $(docker ps -aq)            # 删除所有容器

# 使用场景
# - 清理测试容器
# - 释放系统资源
```

### 🔧 镜像构建命令

#### 9. `docker build` - 构建镜像

```bash
# 语法
docker build [OPTIONS] PATH | URL | -

# 常用参数
-t, --tag         # 指定镜像名称和标签
-f, --file        # 指定Dockerfile路径
--no-cache        # 不使用缓存构建

# 使用示例
docker build -t myapp:v1.0 .          # 构建并标记镜像
docker build -f Dockerfile.prod -t myapp:prod .  # 使用指定Dockerfile
docker build --no-cache -t myapp:latest .        # 不使用缓存构建

# 使用场景
# - 构建自定义应用镜像
# - 创建开发环境镜像
# - CI/CD流水线中的镜像构建
```

#### 10. `docker tag` - 标记镜像

```bash
# 语法
docker tag SOURCE_IMAGE[:TAG] TARGET_IMAGE[:TAG]

# 使用示例
docker tag myapp:v1.0 myapp:latest     # 添加latest标签
docker tag myapp:v1.0 registry.com/myapp:v1.0  # 标记用于推送到仓库

# 使用场景
# - 版本管理
# - 准备推送到仓库
```

### 📦 仓库操作命令

#### 11. `docker push` - 推送镜像到仓库

```bash
# 语法
docker push [OPTIONS] NAME[:TAG]

# 使用示例
docker push myapp:v1.0                 # 推送到Docker Hub
docker push registry.com/myapp:v1.0    # 推送到私有仓库

# 使用场景
# - 分享镜像
# - 部署到生产环境
```

#### 12. `docker login/logout` - 仓库认证

```bash
# 登录
docker login [OPTIONS] [SERVER]
docker login                           # 登录Docker Hub
docker login registry.com              # 登录私有仓库

# 登出
docker logout [SERVER]
docker logout                          # 登出Docker Hub
```

### 🔍 信息查看命令

#### 13. `docker logs` - 查看容器日志

```bash
# 语法
docker logs [OPTIONS] CONTAINER

# 常用参数
-f, --follow      # 实时跟踪日志
-t, --timestamps  # 显示时间戳
--tail            # 显示最后几行

# 使用示例
docker logs mynginx                     # 查看容器日志
docker logs -f mynginx                  # 实时跟踪日志
docker logs --tail 50 mynginx          # 查看最后50行日志

# 使用场景
# - 调试应用问题
# - 监控应用运行状态
```

#### 14. `docker inspect` - 查看详细信息

```bash
# 语法
docker inspect [OPTIONS] NAME|ID [NAME|ID...]

# 使用示例
docker inspect mynginx                  # 查看容器详细信息
docker inspect nginx:latest            # 查看镜像详细信息
docker inspect --format='{{.NetworkSettings.IPAddress}}' mynginx  # 获取IP地址

# 使用场景
# - 获取容器配置信息
# - 网络故障排查
```

#### 15. `docker stats` - 查看资源使用情况

```bash
# 语法
docker stats [OPTIONS] [CONTAINER...]

# 使用示例
docker stats                           # 查看所有运行容器的资源使用
docker stats mynginx                   # 查看指定容器的资源使用
docker stats --no-stream               # 只显示一次，不持续更新

# 使用场景
# - 性能监控
# - 资源优化
```

### 🧹 清理命令

#### 16. `docker system prune` - 清理系统

```bash
# 语法
docker system prune [OPTIONS]

# 常用参数
-a, --all         # 清理所有未使用的资源
-f, --force       # 不询问确认

# 使用示例
docker system prune                     # 清理停止的容器、未使用的网络等
docker system prune -a                  # 清理所有未使用的资源
docker system df                        # 查看磁盘使用情况

# 使用场景
# - 定期清理释放空间
# - 系统维护
```

#### 17. `docker volume` - 数据卷管理

```bash
# 创建数据卷
docker volume create myvolume

# 查看数据卷
docker volume ls

# 删除数据卷
docker volume rm myvolume

# 清理未使用的数据卷
docker volume prune

# 使用示例
docker run -d --name db -v myvolume:/var/lib/mysql mysql:8.0

# 使用场景
# - 数据持久化
# - 容器间数据共享
```

#### 18. `docker network` - 网络管理

```bash
# 查看网络
docker network ls

# 创建网络
docker network create mynetwork

# 删除网络
docker network rm mynetwork

# 连接容器到网络
docker network connect mynetwork mynginx

# 使用场景
# - 容器间通信
# - 网络隔离
```

---

## 4. Dockerfile编写指南

### 📝 Dockerfile基础语法

Dockerfile是用于构建Docker镜像的文本文件，包含了一系列指令和参数。

#### 基本结构

```dockerfile
# 注释以#开头
FROM base_image:tag          # 基础镜像
LABEL key=value             # 元数据标签
RUN command                 # 执行命令
COPY source dest            # 复制文件
ADD source dest             # 添加文件（支持URL和压缩包）
WORKDIR /path               # 设置工作目录
EXPOSE port                 # 声明端口
ENV key=value               # 设置环境变量
CMD ["executable","param"]   # 容器启动命令
ENTRYPOINT ["executable"]   # 入口点
VOLUME ["/data"]            # 数据卷
USER username               # 运行用户
```

### 🔧 核心指令详解

#### 1. FROM - 基础镜像

```dockerfile
# 语法
FROM [--platform=<platform>] <image>[:<tag>] [AS <name>]

# 示例
FROM ubuntu:20.04                    # 使用Ubuntu 20.04作为基础镜像
FROM node:16-alpine AS builder       # 多阶段构建中的命名阶段
FROM scratch                         # 空白镜像，用于构建最小镜像

# 💡 提示：选择合适的基础镜像
# - alpine版本：体积小，安全性高
# - slim版本：去除了不必要的包
# - 官方镜像：稳定性好，更新及时
```

#### 2. RUN - 执行命令

```dockerfile
# Shell形式
RUN apt-get update && apt-get install -y python3

# Exec形式（推荐）
RUN ["apt-get", "update"]
RUN ["apt-get", "install", "-y", "python3"]

# 多命令合并（减少层数）
RUN apt-get update \
    && apt-get install -y \
        python3 \
        python3-pip \
        curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# ⚠️ 注意：每个RUN指令都会创建新的镜像层
```

#### 3. COPY vs ADD

```dockerfile
# COPY - 简单复制（推荐）
COPY app.py /app/                    # 复制文件
COPY requirements.txt /app/          # 复制文件
COPY . /app/                         # 复制当前目录所有内容

# ADD - 高级复制
ADD https://example.com/file.tar.gz /app/  # 支持URL下载
ADD archive.tar.gz /app/             # 自动解压压缩包

# 💡 提示：优先使用COPY，除非需要ADD的特殊功能
```

#### 4. WORKDIR - 工作目录

```dockerfile
WORKDIR /app                         # 设置工作目录
RUN pwd                             # 输出：/app

# 相对路径会基于当前WORKDIR
WORKDIR /app
WORKDIR src                         # 实际路径：/app/src

# 💡 提示：使用绝对路径避免混淆
```

#### 5. ENV - 环境变量

```dockerfile
# 设置单个变量
ENV NODE_ENV=production

# 设置多个变量
ENV NODE_ENV=production \
    PORT=3000 \
    DEBUG=false

# 在其他指令中使用
RUN echo $NODE_ENV
COPY app.py /app/$NODE_ENV/
```

#### 6. EXPOSE - 声明端口

```dockerfile
EXPOSE 80                           # 声明HTTP端口
EXPOSE 443                          # 声明HTTPS端口
EXPOSE 3000/tcp                     # 明确指定协议
EXPOSE 53/udp                       # UDP端口

# ⚠️ 注意：EXPOSE只是声明，不会自动映射端口
# 需要在docker run时使用-p参数映射
```

#### 7. CMD vs ENTRYPOINT

```dockerfile
# CMD - 默认命令（可被覆盖）
CMD ["python3", "app.py"]           # Exec形式（推荐）
CMD python3 app.py                  # Shell形式

# ENTRYPOINT - 入口点（不可覆盖）
ENTRYPOINT ["python3"]
CMD ["app.py"]                      # 作为ENTRYPOINT的参数

# 组合使用示例
ENTRYPOINT ["python3"]
CMD ["app.py"]
# docker run myapp           -> python3 app.py
# docker run myapp test.py   -> python3 test.py
```

### 🏗️ 多阶段构建

多阶段构建可以显著减小最终镜像大小，特别适用于编译型语言。

```dockerfile
# 第一阶段：构建阶段
FROM node:16-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

# 第二阶段：运行阶段
FROM node:16-alpine AS runtime
WORKDIR /app
# 只复制必要的文件
COPY --from=builder /app/node_modules ./node_modules
COPY . .
EXPOSE 3000
CMD ["node", "server.js"]

# Go语言示例
FROM golang:1.19-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o main .

FROM alpine:latest AS runtime
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/main .
CMD ["./main"]
```

### 🚀 镜像优化技巧

#### 1. 减少镜像层数

```dockerfile
# ❌ 不好的做法
RUN apt-get update
RUN apt-get install -y python3
RUN apt-get install -y pip
RUN apt-get clean

# ✅ 好的做法
RUN apt-get update \
    && apt-get install -y python3 pip \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*
```

#### 2. 使用.dockerignore

```bash
# .dockerignore文件内容
node_modules
npm-debug.log
.git
.gitignore
README.md
.env
.nyc_output
coverage
.nyc_output
```

#### 3. 选择合适的基础镜像

```dockerfile
# 大小对比
FROM ubuntu:20.04        # ~72MB
FROM python:3.9          # ~885MB
FROM python:3.9-slim     # ~122MB
FROM python:3.9-alpine   # ~45MB

# 推荐使用alpine版本
FROM python:3.9-alpine
```

#### 4. 合理安排指令顺序

```dockerfile
# ✅ 好的做法：先复制依赖文件，利用缓存
FROM python:3.9-alpine
WORKDIR /app

# 先复制依赖文件
COPY requirements.txt .
RUN pip install -r requirements.txt

# 再复制应用代码
COPY . .

CMD ["python", "app.py"]
```