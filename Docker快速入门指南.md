# 🐳 Docker快速入门指南

> **学习时间安排**：总计12-16小时  
> **Day1**：基础概念和环境搭建（6-8小时）  
> **Day2**：实践应用和项目部署（6-8小时）

---

## 📚 目录

- [1. Docker简介与核心概念](#1-docker简介与核心概念)
- [2. 环境安装与配置](#2-环境安装与配置)
- [3. Docker核心命令详解](#3-docker核心命令详解)
- [4. Dockerfile编写指南](#4-dockerfile编写指南)
- [5. 实践练习项目](#5-实践练习项目)
- [6. 常见问题与故障排除](#6-常见问题与故障排除)
- [7. 学习检查清单](#7-学习检查清单)
- [8. 进阶学习路径](#8-进阶学习路径)

---

## 1. Docker简介与核心概念

### 🎯 什么是Docker？

Docker是一个开源的容器化平台，它允许开发者将应用程序及其依赖项打包到一个轻量级、可移植的容器中，然后可以在任何支持Docker的环境中运行。

### 🔄 容器 vs 虚拟机对比

| 特性 | 容器（Container） | 虚拟机（Virtual Machine） |
|------|------------------|--------------------------|
| **资源占用** | 轻量级，共享主机内核 | 重量级，需要完整操作系统 |
| **启动速度** | 秒级启动 | 分钟级启动 |
| **性能** | 接近原生性能 | 有一定性能损耗 |
| **隔离性** | 进程级隔离 | 硬件级隔离 |
| **可移植性** | 高度可移植 | 相对较低 |

```bash
# 容器架构示意
┌─────────────────────────────────────┐
│           应用程序层                 │
├─────────────────────────────────────┤
│    Container1  │  Container2        │
│   ┌─────────┐  │  ┌─────────┐      │
│   │  App A  │  │  │  App B  │      │
│   │ Runtime │  │  │ Runtime │      │
│   └─────────┘  │  └─────────┘      │
├─────────────────────────────────────┤
│         Docker Engine               │
├─────────────────────────────────────┤
│         Host Operating System       │
└─────────────────────────────────────┘
```

### 🏗️ Docker核心概念

#### 1. 镜像（Image）
- **定义**：只读的模板，用于创建容器
- **特点**：分层存储，支持版本控制
- **类比**：就像程序的安装包

```bash
# 镜像层级结构示例
ubuntu:20.04
├── Layer 1: 基础Ubuntu系统
├── Layer 2: 安装Python
├── Layer 3: 安装应用依赖
└── Layer 4: 复制应用代码
```

#### 2. 容器（Container）
- **定义**：镜像的运行实例
- **特点**：可读写，独立的运行环境
- **类比**：就像正在运行的程序

#### 3. 仓库（Repository）
- **Docker Hub**：官方公共仓库
- **私有仓库**：企业内部使用
- **本地仓库**：开发测试环境

---

## 2. 环境安装与配置

### 💻 Windows安装

```bash
# 1. 下载Docker Desktop for Windows
# 访问：https://www.docker.com/products/docker-desktop

# 2. 系统要求检查
# - Windows 10 64位专业版/企业版/教育版
# - 启用Hyper-V功能
# - 启用WSL 2功能

# 3. 安装验证
docker --version
docker run hello-world
```

### 🍎 macOS安装

```bash
# 1. 下载Docker Desktop for Mac
# 访问：https://www.docker.com/products/docker-desktop

# 2. 使用Homebrew安装（可选）
brew install --cask docker

# 3. 验证安装
docker --version
docker info
```

### 🐧 Linux安装（Ubuntu示例）

```bash
# 1. 更新包索引
sudo apt-get update

# 2. 安装必要的包
sudo apt-get install \
    apt-transport-https \
    ca-certificates \
    curl \
    gnupg \
    lsb-release

# 3. 添加Docker官方GPG密钥
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# 4. 设置稳定版仓库
echo \
  "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu \
  $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 5. 安装Docker Engine
sudo apt-get update
sudo apt-get install docker-ce docker-ce-cli containerd.io

# 6. 验证安装
sudo docker run hello-world

# 7. 添加用户到docker组（避免每次使用sudo）
sudo usermod -aG docker $USER
```

### ⚙️ 配置优化

```bash
# 配置Docker镜像加速器（中国用户推荐）
# 编辑 /etc/docker/daemon.json
{
  "registry-mirrors": [
    "https://mirror.ccs.tencentyun.com",
    "https://registry.docker-cn.com"
  ]
}

# 重启Docker服务
sudo systemctl restart docker
```

---

## 3. Docker核心命令详解

### 📋 镜像管理命令

#### 1. `docker images` - 查看本地镜像

```bash
# 语法
docker images [OPTIONS] [REPOSITORY[:TAG]]

# 常用参数
-a, --all         # 显示所有镜像（包括中间层）
-q, --quiet       # 只显示镜像ID
--format          # 格式化输出

# 使用示例
docker images                    # 查看所有本地镜像
docker images ubuntu            # 查看ubuntu相关镜像
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"

# 使用场景
# - 检查本地有哪些镜像
# - 查看镜像大小和版本
# - 清理不需要的镜像前的确认
```

#### 2. `docker pull` - 拉取镜像

```bash
# 语法
docker pull [OPTIONS] NAME[:TAG|@DIGEST]

# 使用示例
docker pull ubuntu              # 拉取最新版ubuntu
docker pull ubuntu:20.04       # 拉取指定版本
docker pull nginx:alpine       # 拉取轻量版nginx

# 使用场景
# - 获取基础镜像用于构建
# - 更新镜像到最新版本
# - 预先下载需要的镜像
```

#### 3. `docker rmi` - 删除镜像

```bash
# 语法
docker rmi [OPTIONS] IMAGE [IMAGE...]

# 常用参数
-f, --force       # 强制删除

# 使用示例
docker rmi ubuntu:20.04         # 删除指定镜像
docker rmi $(docker images -q)  # 删除所有镜像
docker rmi -f image_id          # 强制删除

# 使用场景
# - 清理不需要的镜像释放空间
# - 删除构建失败的镜像
```

### 🚀 容器管理命令

#### 4. `docker run` - 创建并运行容器

```bash
# 语法
docker run [OPTIONS] IMAGE [COMMAND] [ARG...]

# 常用参数
-d, --detach          # 后台运行
-it                   # 交互式运行
-p, --publish         # 端口映射
-v, --volume          # 数据卷挂载
--name                # 指定容器名称
--rm                  # 容器停止后自动删除
-e, --env             # 设置环境变量

# 使用示例
docker run hello-world                    # 运行测试容器
docker run -it ubuntu bash              # 交互式运行ubuntu
docker run -d --name mynginx -p 8080:80 nginx  # 后台运行nginx并映射端口
docker run --rm -v $(pwd):/app ubuntu ls /app  # 挂载当前目录并执行命令

# 使用场景
# - 快速测试应用
# - 启动服务容器
# - 开发环境搭建
```

#### 5. `docker ps` - 查看容器状态

```bash
# 语法
docker ps [OPTIONS]

# 常用参数
-a, --all         # 显示所有容器（包括停止的）
-q, --quiet       # 只显示容器ID
-l, --latest      # 显示最新创建的容器
--format          # 格式化输出

# 使用示例
docker ps                     # 查看运行中的容器
docker ps -a                  # 查看所有容器
docker ps -q                  # 只显示容器ID
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# 使用场景
# - 检查容器运行状态
# - 获取容器ID用于其他操作
# - 监控容器资源使用
```

#### 6. `docker exec` - 在运行容器中执行命令

```bash
# 语法
docker exec [OPTIONS] CONTAINER COMMAND [ARG...]

# 常用参数
-it               # 交互式执行
-d, --detach      # 后台执行
-u, --user        # 指定用户

# 使用示例
docker exec -it mynginx bash           # 进入nginx容器的bash
docker exec mynginx ls /etc/nginx      # 在容器中执行ls命令
docker exec -u root mynginx whoami     # 以root用户执行命令

# 使用场景
# - 调试运行中的容器
# - 在容器中执行维护任务
# - 查看容器内部状态
```

#### 7. `docker stop/start/restart` - 容器生命周期管理

```bash
# 停止容器
docker stop [OPTIONS] CONTAINER [CONTAINER...]
docker stop mynginx                    # 优雅停止
docker stop -t 30 mynginx             # 30秒后强制停止

# 启动容器
docker start [OPTIONS] CONTAINER [CONTAINER...]
docker start mynginx                   # 启动已停止的容器

# 重启容器
docker restart [OPTIONS] CONTAINER [CONTAINER...]
docker restart mynginx                 # 重启容器

# 使用场景
# - 服务维护和更新
# - 故障恢复
# - 资源管理
```

#### 8. `docker rm` - 删除容器

```bash
# 语法
docker rm [OPTIONS] CONTAINER [CONTAINER...]

# 常用参数
-f, --force       # 强制删除运行中的容器
-v, --volumes     # 删除关联的数据卷

# 使用示例
docker rm mynginx                      # 删除停止的容器
docker rm -f mynginx                   # 强制删除运行中的容器
docker rm $(docker ps -aq)            # 删除所有容器

# 使用场景
# - 清理测试容器
# - 释放系统资源
```

### 🔧 镜像构建命令

#### 9. `docker build` - 构建镜像

```bash
# 语法
docker build [OPTIONS] PATH | URL | -

# 常用参数
-t, --tag         # 指定镜像名称和标签
-f, --file        # 指定Dockerfile路径
--no-cache        # 不使用缓存构建

# 使用示例
docker build -t myapp:v1.0 .          # 构建并标记镜像
docker build -f Dockerfile.prod -t myapp:prod .  # 使用指定Dockerfile
docker build --no-cache -t myapp:latest .        # 不使用缓存构建

# 使用场景
# - 构建自定义应用镜像
# - 创建开发环境镜像
# - CI/CD流水线中的镜像构建
```

#### 10. `docker tag` - 标记镜像

```bash
# 语法
docker tag SOURCE_IMAGE[:TAG] TARGET_IMAGE[:TAG]

# 使用示例
docker tag myapp:v1.0 myapp:latest     # 添加latest标签
docker tag myapp:v1.0 registry.com/myapp:v1.0  # 标记用于推送到仓库

# 使用场景
# - 版本管理
# - 准备推送到仓库
```

### 📦 仓库操作命令

#### 11. `docker push` - 推送镜像到仓库

```bash
# 语法
docker push [OPTIONS] NAME[:TAG]

# 使用示例
docker push myapp:v1.0                 # 推送到Docker Hub
docker push registry.com/myapp:v1.0    # 推送到私有仓库

# 使用场景
# - 分享镜像
# - 部署到生产环境
```

#### 12. `docker login/logout` - 仓库认证

```bash
# 登录
docker login [OPTIONS] [SERVER]
docker login                           # 登录Docker Hub
docker login registry.com              # 登录私有仓库

# 登出
docker logout [SERVER]
docker logout                          # 登出Docker Hub
```

### 🔍 信息查看命令

#### 13. `docker logs` - 查看容器日志

```bash
# 语法
docker logs [OPTIONS] CONTAINER

# 常用参数
-f, --follow      # 实时跟踪日志
-t, --timestamps  # 显示时间戳
--tail            # 显示最后几行

# 使用示例
docker logs mynginx                     # 查看容器日志
docker logs -f mynginx                  # 实时跟踪日志
docker logs --tail 50 mynginx          # 查看最后50行日志

# 使用场景
# - 调试应用问题
# - 监控应用运行状态
```

#### 14. `docker inspect` - 查看详细信息

```bash
# 语法
docker inspect [OPTIONS] NAME|ID [NAME|ID...]

# 使用示例
docker inspect mynginx                  # 查看容器详细信息
docker inspect nginx:latest            # 查看镜像详细信息
docker inspect --format='{{.NetworkSettings.IPAddress}}' mynginx  # 获取IP地址

# 使用场景
# - 获取容器配置信息
# - 网络故障排查
```

#### 15. `docker stats` - 查看资源使用情况

```bash
# 语法
docker stats [OPTIONS] [CONTAINER...]

# 使用示例
docker stats                           # 查看所有运行容器的资源使用
docker stats mynginx                   # 查看指定容器的资源使用
docker stats --no-stream               # 只显示一次，不持续更新

# 使用场景
# - 性能监控
# - 资源优化
```

### 🧹 清理命令

#### 16. `docker system prune` - 清理系统

```bash
# 语法
docker system prune [OPTIONS]

# 常用参数
-a, --all         # 清理所有未使用的资源
-f, --force       # 不询问确认

# 使用示例
docker system prune                     # 清理停止的容器、未使用的网络等
docker system prune -a                  # 清理所有未使用的资源
docker system df                        # 查看磁盘使用情况

# 使用场景
# - 定期清理释放空间
# - 系统维护
```

#### 17. `docker volume` - 数据卷管理

```bash
# 创建数据卷
docker volume create myvolume

# 查看数据卷
docker volume ls

# 删除数据卷
docker volume rm myvolume

# 清理未使用的数据卷
docker volume prune

# 使用示例
docker run -d --name db -v myvolume:/var/lib/mysql mysql:8.0

# 使用场景
# - 数据持久化
# - 容器间数据共享
```

#### 18. `docker network` - 网络管理

```bash
# 查看网络
docker network ls

# 创建网络
docker network create mynetwork

# 删除网络
docker network rm mynetwork

# 连接容器到网络
docker network connect mynetwork mynginx

# 使用场景
# - 容器间通信
# - 网络隔离
```

---

## 4. Dockerfile编写指南

### 📝 Dockerfile基础语法

Dockerfile是用于构建Docker镜像的文本文件，包含了一系列指令和参数。

#### 基本结构

```dockerfile
# 注释以#开头
FROM base_image:tag          # 基础镜像
MAINTAINER author_info       # 维护者信息（已废弃，推荐使用LABEL）
LABEL key=value             # 元数据标签
RUN command                 # 执行命令
COPY source dest            # 复制文件
ADD source dest             # 添加文件（支持URL和压缩包）
WORKDIR /path               # 设置工作目录
EXPOSE port                 # 声明端口
ENV key=value               # 设置环境变量
CMD ["executable","param"]   # 容器启动命令
ENTRYPOINT ["executable"]   # 入口点
VOLUME ["/data"]            # 数据卷
USER username               # 运行用户
```

### 🔧 核心指令详解

#### 1. FROM - 基础镜像

```dockerfile
# 语法
FROM [--platform=<platform>] <image>[:<tag>] [AS <name>]

# 示例
FROM ubuntu:20.04                    # 使用Ubuntu 20.04作为基础镜像
FROM node:16-alpine AS builder       # 多阶段构建中的命名阶段
FROM scratch                         # 空白镜像，用于构建最小镜像

# 💡 提示：选择合适的基础镜像
# - alpine版本：体积小，安全性高
# - slim版本：去除了不必要的包
# - 官方镜像：稳定性好，更新及时
```

#### 2. RUN - 执行命令

```dockerfile
# Shell形式
RUN apt-get update && apt-get install -y python3

# Exec形式（推荐）
RUN ["apt-get", "update"]
RUN ["apt-get", "install", "-y", "python3"]

# 多命令合并（减少层数）
RUN apt-get update \
    && apt-get install -y \
        python3 \
        python3-pip \
        curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# ⚠️ 注意：每个RUN指令都会创建新的镜像层
```

#### 3. COPY vs ADD

```dockerfile
# COPY - 简单复制（推荐）
COPY app.py /app/                    # 复制文件
COPY requirements.txt /app/          # 复制文件
COPY . /app/                         # 复制当前目录所有内容

# ADD - 高级复制
ADD https://example.com/file.tar.gz /app/  # 支持URL下载
ADD archive.tar.gz /app/             # 自动解压压缩包

# 💡 提示：优先使用COPY，除非需要ADD的特殊功能
```

#### 4. WORKDIR - 工作目录

```dockerfile
WORKDIR /app                         # 设置工作目录
RUN pwd                             # 输出：/app

# 相对路径会基于当前WORKDIR
WORKDIR /app
WORKDIR src                         # 实际路径：/app/src

# 💡 提示：使用绝对路径避免混淆
```

#### 5. ENV - 环境变量

```dockerfile
# 设置单个变量
ENV NODE_ENV=production

# 设置多个变量
ENV NODE_ENV=production \
    PORT=3000 \
    DEBUG=false

# 在其他指令中使用
RUN echo $NODE_ENV
COPY app.py /app/$NODE_ENV/
```

#### 6. EXPOSE - 声明端口

```dockerfile
EXPOSE 80                           # 声明HTTP端口
EXPOSE 443                          # 声明HTTPS端口
EXPOSE 3000/tcp                     # 明确指定协议
EXPOSE 53/udp                       # UDP端口

# ⚠️ 注意：EXPOSE只是声明，不会自动映射端口
# 需要在docker run时使用-p参数映射
```

#### 7. CMD vs ENTRYPOINT

```dockerfile
# CMD - 默认命令（可被覆盖）
CMD ["python3", "app.py"]           # Exec形式（推荐）
CMD python3 app.py                  # Shell形式

# ENTRYPOINT - 入口点（不可覆盖）
ENTRYPOINT ["python3"]
CMD ["app.py"]                      # 作为ENTRYPOINT的参数

# 组合使用示例
ENTRYPOINT ["python3"]
CMD ["app.py"]
# docker run myapp           -> python3 app.py
# docker run myapp test.py   -> python3 test.py
```

### 🏗️ 多阶段构建

多阶段构建可以显著减小最终镜像大小，特别适用于编译型语言。

```dockerfile
# 第一阶段：构建阶段
FROM node:16-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

# 第二阶段：运行阶段
FROM node:16-alpine AS runtime
WORKDIR /app
# 只复制必要的文件
COPY --from=builder /app/node_modules ./node_modules
COPY . .
EXPOSE 3000
CMD ["node", "server.js"]

# Go语言示例
FROM golang:1.19-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o main .

FROM alpine:latest AS runtime
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/main .
CMD ["./main"]
```

### 🚀 镜像优化技巧

#### 1. 减少镜像层数

```dockerfile
# ❌ 不好的做法
RUN apt-get update
RUN apt-get install -y python3
RUN apt-get install -y pip
RUN apt-get clean

# ✅ 好的做法
RUN apt-get update \
    && apt-get install -y python3 pip \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*
```

#### 2. 使用.dockerignore

```bash
# .dockerignore文件内容
node_modules
npm-debug.log
.git
.gitignore
README.md
.env
.nyc_output
coverage
.nyc_output
```

#### 3. 选择合适的基础镜像

```dockerfile
# 大小对比
FROM ubuntu:20.04        # ~72MB
FROM python:3.9          # ~885MB
FROM python:3.9-slim     # ~122MB
FROM python:3.9-alpine   # ~45MB

# 推荐使用alpine版本
FROM python:3.9-alpine
```

#### 4. 合理安排指令顺序

```dockerfile
# ✅ 好的做法：先复制依赖文件，利用缓存
FROM python:3.9-alpine
WORKDIR /app

# 先复制依赖文件
COPY requirements.txt .
RUN pip install -r requirements.txt

# 再复制应用代码
COPY . .

CMD ["python", "app.py"]
```

### 📋 完整示例

#### Python Flask应用

```dockerfile
# 多阶段构建的Python应用
FROM python:3.9-alpine AS base

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 安装系统依赖
RUN apk add --no-cache \
    gcc \
    musl-dev \
    postgresql-dev

# 构建阶段
FROM base AS builder
WORKDIR /app
COPY requirements.txt .
RUN pip install --user -r requirements.txt

# 运行阶段
FROM base AS runtime
WORKDIR /app

# 创建非root用户
RUN addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup

# 复制依赖
COPY --from=builder /root/.local /home/<USER>/.local
ENV PATH=/home/<USER>/.local/bin:$PATH

# 复制应用代码
COPY --chown=appuser:appgroup . .

# 切换到非root用户
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

EXPOSE 5000
CMD ["python", "app.py"]
```

#### Node.js应用

```dockerfile
FROM node:16-alpine AS base

# 安装dumb-init用于信号处理
RUN apk add --no-cache dumb-init

# 创建应用目录
WORKDIR /app

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001 -G nodejs

# 依赖安装阶段
FROM base AS deps
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# 构建阶段
FROM base AS builder
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

# 运行阶段
FROM base AS runtime
ENV NODE_ENV=production

# 复制必要文件
COPY --from=deps --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nextjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nextjs:nodejs /app/package.json ./

USER nextjs

EXPOSE 3000

ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/server.js"]
```

---

## 5. 实践练习项目

### 🎯 练习目标
通过5个循序渐进的实践项目，从基础容器操作到复杂的多容器应用部署，全面掌握Docker的使用。

### 📚 练习1：Hello Docker - 基础容器操作

**目标**：熟悉基本的Docker命令和容器生命周期管理

**预计时间**：30分钟

```bash
# 1. 运行第一个容器
docker run hello-world

# 2. 交互式运行Ubuntu容器
docker run -it --name my-ubuntu ubuntu:20.04 bash

# 在容器内执行以下命令
apt update
apt install -y curl
curl --version
exit

# 3. 查看容器状态
docker ps -a

# 4. 重新启动容器
docker start my-ubuntu
docker exec -it my-ubuntu bash

# 5. 停止和删除容器
docker stop my-ubuntu
docker rm my-ubuntu

# 6. 清理镜像
docker rmi ubuntu:20.04
```

**✅ 检查点**：
- [ ] 能够成功运行hello-world容器
- [ ] 理解交互式容器的使用
- [ ] 掌握容器的启动、停止、删除操作
- [ ] 了解容器和镜像的区别

---

### 📚 练习2：Web服务器部署 - 端口映射和数据卷

**目标**：学习端口映射、数据卷挂载和环境变量设置

**预计时间**：45分钟

#### 项目结构
```
web-server-demo/
├── html/
│   ├── index.html
│   ├── about.html
│   └── css/
│       └── style.css
└── nginx.conf
```

#### 创建项目文件

```bash
# 创建项目目录
mkdir web-server-demo
cd web-server-demo
mkdir html html/css
```

**html/index.html**
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Docker Web Server Demo</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <h1>🐳 欢迎来到Docker世界！</h1>
        <p>这是一个运行在Docker容器中的网站</p>
        <nav>
            <a href="index.html">首页</a>
            <a href="about.html">关于</a>
        </nav>
        <div class="info">
            <h2>容器信息</h2>
            <p>服务器：Nginx</p>
            <p>端口：80 (映射到主机8080)</p>
            <p>数据卷：/usr/share/nginx/html</p>
        </div>
    </div>
</body>
</html>
```

**html/about.html**
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关于 - Docker Demo</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <h1>关于Docker</h1>
        <p>Docker是一个开源的容器化平台</p>
        <nav>
            <a href="index.html">首页</a>
            <a href="about.html">关于</a>
        </nav>
        <ul>
            <li>轻量级虚拟化技术</li>
            <li>快速部署应用</li>
            <li>环境一致性保证</li>
            <li>微服务架构支持</li>
        </ul>
    </div>
</body>
</html>
```

**html/css/style.css**
```css
body {
    font-family: 'Arial', sans-serif;
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    min-height: 100vh;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    text-align: center;
}

h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

nav {
    margin: 2rem 0;
}

nav a {
    color: white;
    text-decoration: none;
    margin: 0 1rem;
    padding: 0.5rem 1rem;
    border: 2px solid white;
    border-radius: 5px;
    transition: all 0.3s ease;
}

nav a:hover {
    background: white;
    color: #667eea;
}

.info {
    background: rgba(255,255,255,0.1);
    padding: 1.5rem;
    border-radius: 10px;
    margin-top: 2rem;
}

ul {
    text-align: left;
    max-width: 300px;
    margin: 0 auto;
}

li {
    margin: 0.5rem 0;
    padding: 0.5rem;
    background: rgba(255,255,255,0.1);
    border-radius: 5px;
}
```

#### 部署步骤

```bash
# 1. 基础部署（端口映射）
docker run -d --name my-nginx -p 8080:80 nginx:alpine

# 访问 http://localhost:8080 查看默认页面

# 2. 停止容器并删除
docker stop my-nginx
docker rm my-nginx

# 3. 挂载自定义网页
docker run -d --name my-web-server \
  -p 8080:80 \
  -v $(pwd)/html:/usr/share/nginx/html \
  nginx:alpine

# 4. 查看容器日志
docker logs my-web-server

# 5. 实时查看访问日志
docker logs -f my-web-server

# 6. 进入容器查看文件
docker exec -it my-web-server sh
ls -la /usr/share/nginx/html
exit

# 7. 修改文件测试热更新
echo "<h2>文件已更新！</h2>" >> html/index.html

# 8. 使用环境变量
docker stop my-web-server
docker rm my-web-server

docker run -d --name my-web-server \
  -p 8080:80 \
  -v $(pwd)/html:/usr/share/nginx/html \
  -e NGINX_HOST=localhost \
  -e NGINX_PORT=80 \
  nginx:alpine
```

**✅ 检查点**：
- [ ] 能够成功映射端口并访问网站
- [ ] 理解数据卷挂载的作用
- [ ] 掌握容器日志查看方法
- [ ] 了解环境变量的设置和使用

---

### 📚 练习3：自定义应用镜像 - Dockerfile实战

**目标**：编写Dockerfile构建自定义Python Web应用镜像

**预计时间**：60分钟

#### 项目结构
```
python-web-app/
├── app.py
├── requirements.txt
├── templates/
│   ├── index.html
│   └── api.html
├── static/
│   └── style.css
├── Dockerfile
└── .dockerignore
```

#### 创建项目文件

```bash
mkdir python-web-app
cd python-web-app
mkdir templates static
```

**requirements.txt**
```txt
Flask==2.3.3
requests==2.31.0
gunicorn==21.2.0
```

**app.py**
```python
from flask import Flask, render_template, jsonify
import requests
import os
import socket
from datetime import datetime

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api')
def api_page():
    return render_template('api.html')

@app.route('/api/info')
def get_info():
    """获取容器信息API"""
    try:
        hostname = socket.gethostname()
        ip_address = socket.gethostbyname(hostname)

        info = {
            'container_id': hostname,
            'ip_address': ip_address,
            'timestamp': datetime.now().isoformat(),
            'python_version': os.sys.version,
            'environment': os.environ.get('FLASK_ENV', 'production'),
            'port': os.environ.get('PORT', '5000')
        }
        return jsonify(info)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/health')
def health_check():
    """健康检查API"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/external')
def external_api():
    """调用外部API示例"""
    try:
        response = requests.get('https://httpbin.org/json', timeout=5)
        return jsonify({
            'external_data': response.json(),
            'status_code': response.status_code
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('FLASK_ENV') == 'development'
    app.run(host='0.0.0.0', port=port, debug=debug)
```

**templates/index.html**
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python Docker App</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <h1>🐍 Python Flask Docker应用</h1>
        <p>这是一个运行在Docker容器中的Python Web应用</p>

        <div class="nav">
            <a href="/">首页</a>
            <a href="/api">API测试</a>
        </div>

        <div class="card">
            <h2>应用特性</h2>
            <ul>
                <li>Flask Web框架</li>
                <li>RESTful API接口</li>
                <li>容器信息展示</li>
                <li>外部API调用</li>
                <li>健康检查端点</li>
            </ul>
        </div>

        <div class="card">
            <h2>快速测试</h2>
            <button onclick="loadInfo()">获取容器信息</button>
            <button onclick="healthCheck()">健康检查</button>
            <button onclick="externalAPI()">外部API测试</button>
            <div id="result"></div>
        </div>
    </div>

    <script>
        async function loadInfo() {
            try {
                const response = await fetch('/api/info');
                const data = await response.json();
                document.getElementById('result').innerHTML =
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML =
                    '<p class="error">错误: ' + error.message + '</p>';
            }
        }

        async function healthCheck() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                document.getElementById('result').innerHTML =
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML =
                    '<p class="error">错误: ' + error.message + '</p>';
            }
        }

        async function externalAPI() {
            try {
                const response = await fetch('/api/external');
                const data = await response.json();
                document.getElementById('result').innerHTML =
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML =
                    '<p class="error">错误: ' + error.message + '</p>';
            }
        }
    </script>
</body>
</html>
```

**templates/api.html**
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试 - Python Docker App</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <h1>🔌 API接口测试</h1>

        <div class="nav">
            <a href="/">首页</a>
            <a href="/api">API测试</a>
        </div>

        <div class="api-section">
            <h2>可用的API端点</h2>
            <div class="endpoint">
                <h3>GET /api/info</h3>
                <p>获取容器详细信息</p>
                <button onclick="testAPI('/api/info')">测试</button>
            </div>

            <div class="endpoint">
                <h3>GET /api/health</h3>
                <p>健康检查端点</p>
                <button onclick="testAPI('/api/health')">测试</button>
            </div>

            <div class="endpoint">
                <h3>GET /api/external</h3>
                <p>外部API调用示例</p>
                <button onclick="testAPI('/api/external')">测试</button>
            </div>
        </div>

        <div class="result-section">
            <h2>测试结果</h2>
            <div id="api-result"></div>
        </div>
    </div>

    <script>
        async function testAPI(endpoint) {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = '<p>正在请求 ' + endpoint + '...</p>';

            try {
                const startTime = Date.now();
                const response = await fetch(endpoint);
                const endTime = Date.now();
                const data = await response.json();

                resultDiv.innerHTML = `
                    <div class="api-response">
                        <h3>请求: ${endpoint}</h3>
                        <p><strong>状态码:</strong> ${response.status}</p>
                        <p><strong>响应时间:</strong> ${endTime - startTime}ms</p>
                        <h4>响应数据:</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>请求失败: ${endpoint}</h3>
                        <p>错误信息: ${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
```

**static/style.css**
```css
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
}

h1 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 2rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.nav {
    text-align: center;
    margin: 2rem 0;
}

.nav a {
    color: white;
    text-decoration: none;
    margin: 0 1rem;
    padding: 0.8rem 1.5rem;
    border: 2px solid white;
    border-radius: 25px;
    transition: all 0.3s ease;
    display: inline-block;
}

.nav a:hover {
    background: white;
    color: #667eea;
    transform: translateY(-2px);
}

.card {
    background: rgba(255,255,255,0.1);
    padding: 2rem;
    border-radius: 15px;
    margin: 2rem 0;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.card h2 {
    margin-bottom: 1rem;
    color: #fff;
}

.card ul {
    list-style: none;
}

.card li {
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.card li:before {
    content: "✓ ";
    color: #4CAF50;
    font-weight: bold;
}

button {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    margin: 0.5rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

#result, #api-result {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(0,0,0,0.2);
    border-radius: 10px;
    min-height: 100px;
}

pre {
    background: rgba(0,0,0,0.3);
    padding: 1rem;
    border-radius: 5px;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.error {
    color: #ff6b6b;
    background: rgba(255,107,107,0.1);
    padding: 1rem;
    border-radius: 5px;
    border-left: 4px solid #ff6b6b;
}

.api-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.endpoint {
    background: rgba(255,255,255,0.1);
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
}

.endpoint h3 {
    color: #4CAF50;
    margin-bottom: 0.5rem;
    font-family: 'Courier New', monospace;
}

.api-response {
    background: rgba(255,255,255,0.05);
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
}

.result-section {
    margin-top: 2rem;
}

@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }

    h1 {
        font-size: 2rem;
    }

    .nav a {
        display: block;
        margin: 0.5rem 0;
    }

    .api-section {
        grid-template-columns: 1fr;
    }
}
```

**Dockerfile**
```dockerfile
# 多阶段构建的Python应用
FROM python:3.11-alpine AS base

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 安装系统依赖
RUN apk add --no-cache \
    gcc \
    musl-dev \
    linux-headers

# 依赖安装阶段
FROM base AS deps
WORKDIR /app
COPY requirements.txt .
RUN pip install --user -r requirements.txt

# 运行阶段
FROM base AS runtime
WORKDIR /app

# 创建非root用户
RUN addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup

# 复制Python依赖
COPY --from=deps /root/.local /home/<USER>/.local
ENV PATH=/home/<USER>/.local/bin:$PATH

# 复制应用代码
COPY --chown=appuser:appgroup . .

# 切换到非root用户
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:5000/api/health', timeout=5)"

# 暴露端口
EXPOSE 5000

# 启动命令
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "2", "--timeout", "60", "app:app"]
```

**.dockerignore**
```
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

.DS_Store
.vscode
*.swp
*.swo

README.md
Dockerfile
.dockerignore
```

#### 构建和运行步骤

```bash
# 1. 构建镜像
docker build -t python-web-app:v1.0 .

# 2. 查看构建的镜像
docker images python-web-app

# 3. 运行容器
docker run -d --name my-python-app -p 5000:5000 python-web-app:v1.0

# 4. 查看容器状态
docker ps

# 5. 查看应用日志
docker logs my-python-app

# 6. 测试应用
curl http://localhost:5000/api/health
curl http://localhost:5000/api/info

# 7. 进入容器调试
docker exec -it my-python-app sh

# 8. 使用环境变量
docker stop my-python-app
docker rm my-python-app

docker run -d --name my-python-app \
  -p 5000:5000 \
  -e FLASK_ENV=development \
  -e PORT=5000 \
  python-web-app:v1.0

# 9. 查看镜像构建历史
docker history python-web-app:v1.0

# 10. 标记镜像版本
docker tag python-web-app:v1.0 python-web-app:latest
```

**✅ 检查点**：
- [ ] 能够成功编写和构建Dockerfile
- [ ] 理解多阶段构建的优势
- [ ] 掌握镜像优化技巧
- [ ] 了解健康检查的配置
- [ ] 能够调试容器中的应用

---

### 📚 练习4：Docker Compose - 多容器应用编排

**目标**：使用Docker Compose部署包含Web应用、数据库和缓存的完整应用栈

**预计时间**：75分钟

#### 项目结构
```
fullstack-app/
├── web/
│   ├── app.py
│   ├── requirements.txt
│   ├── templates/
│   └── Dockerfile
├── nginx/
│   └── nginx.conf
├── docker-compose.yml
├── docker-compose.override.yml
├── .env
└── init-db/
    └── init.sql
```

#### 创建项目文件

```bash
mkdir fullstack-app
cd fullstack-app
mkdir web nginx init-db
mkdir web/templates
```

**docker-compose.yml**
```yaml
version: '3.8'

services:
  # Web应用服务
  web:
    build:
      context: ./web
      dockerfile: Dockerfile
    container_name: fullstack-web
    environment:
      - FLASK_ENV=${FLASK_ENV:-production}
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@db:5432/${POSTGRES_DB}
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    volumes:
      - ./web:/app
    networks:
      - app-network
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: fullstack-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - web
    networks:
      - app-network
    restart: unless-stopped

  # PostgreSQL数据库
  db:
    image: postgres:15-alpine
    container_name: fullstack-db
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db:/docker-entrypoint-initdb.d
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: fullstack-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 数据库管理工具
  adminer:
    image: adminer:latest
    container_name: fullstack-adminer
    ports:
      - "8080:8080"
    depends_on:
      - db
    networks:
      - app-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  app-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

**.env**
```env
# 应用配置
FLASK_ENV=development
APP_SECRET_KEY=your-secret-key-here

# 数据库配置
POSTGRES_DB=fullstack_app
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres123
DATABASE_URL=*****************************************/fullstack_app

# Redis配置
REDIS_PASSWORD=redis123
REDIS_URL=redis://:redis123@redis:6379/0

# 其他配置
COMPOSE_PROJECT_NAME=fullstack
```

**docker-compose.override.yml** (开发环境覆盖配置)
```yaml
version: '3.8'

services:
  web:
    environment:
      - FLASK_ENV=development
      - FLASK_DEBUG=1
    volumes:
      - ./web:/app
    ports:
      - "5000:5000"  # 开发时直接暴露端口
    command: python app.py

  nginx:
    ports:
      - "8000:80"  # 开发环境使用不同端口
```

**web/requirements.txt**
```txt
Flask==2.3.3
psycopg2-binary==2.9.7
redis==4.6.0
SQLAlchemy==2.0.21
Flask-SQLAlchemy==3.0.5
gunicorn==21.2.0
```

**web/app.py**
```python
from flask import Flask, render_template, request, jsonify
from flask_sqlalchemy import SQLAlchemy
import redis
import os
import json
from datetime import datetime

app = Flask(__name__)

# 配置
app.config['SECRET_KEY'] = os.environ.get('APP_SECRET_KEY', 'dev-secret-key')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# 初始化扩展
db = SQLAlchemy(app)

# Redis连接
try:
    redis_client = redis.from_url(os.environ.get('REDIS_URL', 'redis://localhost:6379/0'))
    redis_client.ping()
    redis_available = True
except:
    redis_available = False

# 数据模型
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'created_at': self.created_at.isoformat()
        }

class Post(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    content = db.Column(db.Text, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    user = db.relationship('User', backref=db.backref('posts', lazy=True))

    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'user_id': self.user_id,
            'username': self.user.username,
            'created_at': self.created_at.isoformat()
        }

# 路由
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/users', methods=['GET', 'POST'])
def users():
    if request.method == 'POST':
        data = request.get_json()
        user = User(username=data['username'], email=data['email'])
        db.session.add(user)
        db.session.commit()

        # 缓存用户信息
        if redis_available:
            redis_client.setex(f"user:{user.id}", 3600, json.dumps(user.to_dict()))

        return jsonify(user.to_dict()), 201

    users = User.query.all()
    return jsonify([user.to_dict() for user in users])

@app.route('/api/posts', methods=['GET', 'POST'])
def posts():
    if request.method == 'POST':
        data = request.get_json()
        post = Post(title=data['title'], content=data['content'], user_id=data['user_id'])
        db.session.add(post)
        db.session.commit()
        return jsonify(post.to_dict()), 201

    posts = Post.query.join(User).all()
    return jsonify([post.to_dict() for post in posts])

@app.route('/api/stats')
def stats():
    user_count = User.query.count()
    post_count = Post.query.count()

    # 从缓存获取访问统计
    visit_count = 0
    if redis_available:
        try:
            visit_count = redis_client.incr('visit_count')
        except:
            pass

    return jsonify({
        'users': user_count,
        'posts': post_count,
        'visits': visit_count,
        'redis_available': redis_available,
        'database_connected': True
    })

@app.route('/api/health')
def health():
    # 检查数据库连接
    try:
        db.session.execute('SELECT 1')
        db_status = 'healthy'
    except:
        db_status = 'unhealthy'

    # 检查Redis连接
    redis_status = 'healthy' if redis_available else 'unavailable'

    return jsonify({
        'status': 'healthy' if db_status == 'healthy' else 'unhealthy',
        'database': db_status,
        'redis': redis_status,
        'timestamp': datetime.utcnow().isoformat()
    })

# 创建表
with app.app_context():
    db.create_all()

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('FLASK_ENV') == 'development'
    app.run(host='0.0.0.0', port=port, debug=debug)
```

**web/templates/index.html**
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全栈Docker应用</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        h1 { text-align: center; font-size: 2.5rem; margin-bottom: 2rem; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; }
        .card {
            background: rgba(255,255,255,0.1);
            padding: 2rem;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .stats { display: flex; justify-content: space-around; margin: 2rem 0; }
        .stat { text-align: center; }
        .stat-number { font-size: 2rem; font-weight: bold; color: #4CAF50; }
        button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            margin: 0.5rem;
            font-size: 1rem;
        }
        input, textarea {
            width: 100%;
            padding: 0.8rem;
            margin: 0.5rem 0;
            border: none;
            border-radius: 5px;
            background: rgba(255,255,255,0.9);
            color: #333;
        }
        .result {
            background: rgba(0,0,0,0.2);
            padding: 1rem;
            border-radius: 10px;
            margin-top: 1rem;
            max-height: 300px;
            overflow-y: auto;
        }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐳 全栈Docker应用演示</h1>

        <div class="stats" id="stats">
            <div class="stat">
                <div class="stat-number" id="user-count">-</div>
                <div>用户数</div>
            </div>
            <div class="stat">
                <div class="stat-number" id="post-count">-</div>
                <div>文章数</div>
            </div>
            <div class="stat">
                <div class="stat-number" id="visit-count">-</div>
                <div>访问数</div>
            </div>
        </div>

        <div class="grid">
            <div class="card">
                <h2>👥 用户管理</h2>
                <input type="text" id="username" placeholder="用户名">
                <input type="email" id="email" placeholder="邮箱">
                <button onclick="createUser()">创建用户</button>
                <button onclick="loadUsers()">查看用户</button>
                <div class="result" id="user-result"></div>
            </div>

            <div class="card">
                <h2>📝 文章管理</h2>
                <input type="text" id="post-title" placeholder="文章标题">
                <textarea id="post-content" placeholder="文章内容" rows="3"></textarea>
                <input type="number" id="post-user-id" placeholder="作者ID">
                <button onclick="createPost()">发布文章</button>
                <button onclick="loadPosts()">查看文章</button>
                <div class="result" id="post-result"></div>
            </div>

            <div class="card">
                <h2>📊 系统状态</h2>
                <button onclick="loadStats()">刷新统计</button>
                <button onclick="healthCheck()">健康检查</button>
                <div class="result" id="system-result"></div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时获取统计信息
        window.onload = function() {
            loadStats();
        };

        async function createUser() {
            const username = document.getElementById('username').value;
            const email = document.getElementById('email').value;

            if (!username || !email) {
                alert('请填写用户名和邮箱');
                return;
            }

            try {
                const response = await fetch('/api/users', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, email })
                });
                const data = await response.json();
                document.getElementById('user-result').innerHTML =
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';

                // 清空输入框
                document.getElementById('username').value = '';
                document.getElementById('email').value = '';

                // 刷新统计
                loadStats();
            } catch (error) {
                document.getElementById('user-result').innerHTML =
                    '<p style="color: #ff6b6b;">错误: ' + error.message + '</p>';
            }
        }

        async function loadUsers() {
            try {
                const response = await fetch('/api/users');
                const data = await response.json();
                document.getElementById('user-result').innerHTML =
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('user-result').innerHTML =
                    '<p style="color: #ff6b6b;">错误: ' + error.message + '</p>';
            }
        }

        async function createPost() {
            const title = document.getElementById('post-title').value;
            const content = document.getElementById('post-content').value;
            const user_id = parseInt(document.getElementById('post-user-id').value);

            if (!title || !content || !user_id) {
                alert('请填写所有字段');
                return;
            }

            try {
                const response = await fetch('/api/posts', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ title, content, user_id })
                });
                const data = await response.json();
                document.getElementById('post-result').innerHTML =
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';

                // 清空输入框
                document.getElementById('post-title').value = '';
                document.getElementById('post-content').value = '';
                document.getElementById('post-user-id').value = '';

                // 刷新统计
                loadStats();
            } catch (error) {
                document.getElementById('post-result').innerHTML =
                    '<p style="color: #ff6b6b;">错误: ' + error.message + '</p>';
            }
        }

        async function loadPosts() {
            try {
                const response = await fetch('/api/posts');
                const data = await response.json();
                document.getElementById('post-result').innerHTML =
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('post-result').innerHTML =
                    '<p style="color: #ff6b6b;">错误: ' + error.message + '</p>';
            }
        }

        async function loadStats() {
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();

                document.getElementById('user-count').textContent = data.users;
                document.getElementById('post-count').textContent = data.posts;
                document.getElementById('visit-count').textContent = data.visits;

                document.getElementById('system-result').innerHTML =
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('system-result').innerHTML =
                    '<p style="color: #ff6b6b;">错误: ' + error.message + '</p>';
            }
        }

        async function healthCheck() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                document.getElementById('system-result').innerHTML =
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('system-result').innerHTML =
                    '<p style="color: #ff6b6b;">错误: ' + error.message + '</p>';
            }
        }
    </script>
</body>
</html>
```

**web/Dockerfile**
```dockerfile
FROM python:3.11-alpine

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

# 安装系统依赖
RUN apk add --no-cache \
    postgresql-dev \
    gcc \
    musl-dev \
    linux-headers

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非root用户
RUN addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup && \
    chown -R appuser:appgroup /app

USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:5000/api/health', timeout=5)"

EXPOSE 5000

CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "2", "app:app"]
```

**nginx/nginx.conf**
```nginx
events {
    worker_connections 1024;
}

http {
    upstream web {
        server web:5000;
    }

    server {
        listen 80;
        server_name localhost;

        location / {
            proxy_pass http://web;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
```

**init-db/init.sql**
```sql
-- 初始化数据库脚本
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 插入示例数据
INSERT INTO user (username, email, created_at) VALUES
('admin', '<EMAIL>', NOW()),
('user1', '<EMAIL>', NOW()),
('user2', '<EMAIL>', NOW());

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_username ON user(username);
CREATE INDEX IF NOT EXISTS idx_user_email ON user(email);
CREATE INDEX IF NOT EXISTS idx_post_user_id ON post(user_id);
CREATE INDEX IF NOT EXISTS idx_post_created_at ON post(created_at);
```

#### 部署和管理步骤

```bash
# 1. 启动所有服务
docker-compose up -d

# 2. 查看服务状态
docker-compose ps

# 3. 查看日志
docker-compose logs -f web
docker-compose logs -f db

# 4. 进入Web容器
docker-compose exec web sh

# 5. 进入数据库容器
docker-compose exec db psql -U postgres -d fullstack_app

# 6. 扩展Web服务
docker-compose up -d --scale web=3

# 7. 重启特定服务
docker-compose restart web

# 8. 停止所有服务
docker-compose down

# 9. 停止并删除数据卷
docker-compose down -v

# 10. 生产环境部署
docker-compose -f docker-compose.yml up -d
```

**✅ 检查点**：
- [ ] 能够使用Docker Compose编排多容器应用
- [ ] 理解服务间的依赖关系和网络通信
- [ ] 掌握数据卷的使用和数据持久化
- [ ] 了解环境变量和配置管理
- [ ] 能够进行服务扩展和负载均衡

---

### 📚 练习5：生产环境部署 - 完整CI/CD流水线

**目标**：构建完整的CI/CD流水线，实现从代码提交到生产部署的自动化

**预计时间**：90分钟

#### 项目结构
```
production-app/
├── .github/
│   └── workflows/
│       ├── ci.yml
│       └── cd.yml
├── app/
│   ├── Dockerfile
│   ├── Dockerfile.prod
│   └── ...
├── k8s/
│   ├── namespace.yaml
│   ├── deployment.yaml
│   ├── service.yaml
│   └── ingress.yaml
├── docker-compose.prod.yml
├── Makefile
└── scripts/
    ├── build.sh
    ├── deploy.sh
    └── health-check.sh
```

这个练习将在后续的Kubernetes和CI/CD专门教程中详细展开。

**✅ 检查点**：
- [ ] 理解CI/CD流水线的概念
- [ ] 了解生产环境部署的考虑因素
- [ ] 掌握多环境配置管理
- [ ] 了解监控和日志收集的重要性

---

## 6. 常见问题与故障排除

### 🚨 问题1：Docker服务无法启动

**错误信息**：
```bash
Cannot connect to the Docker daemon at unix:///var/run/docker.sock. Is the docker daemon running?
```

**解决步骤**：

**Windows/macOS**：
```bash
# 1. 检查Docker Desktop是否运行
# 查看系统托盘是否有Docker图标

# 2. 重启Docker Desktop
# 右键Docker图标 -> Restart

# 3. 检查系统要求
# Windows: 确保启用了Hyper-V和WSL 2
# macOS: 确保系统版本支持

# 4. 重新安装Docker Desktop（如果以上步骤无效）
```

**Linux**：
```bash
# 1. 检查Docker服务状态
sudo systemctl status docker

# 2. 启动Docker服务
sudo systemctl start docker

# 3. 设置开机自启
sudo systemctl enable docker

# 4. 检查用户权限
sudo usermod -aG docker $USER
# 注销并重新登录

# 5. 检查Docker版本
docker --version
```

---

### 🚨 问题2：端口冲突

**错误信息**：
```bash
Error response from daemon: driver failed programming external connectivity on endpoint:
bind for 0.0.0.0:8080 failed: port is already allocated
```

**解决步骤**：

```bash
# 1. 查看端口占用情况
# Windows
netstat -ano | findstr :8080

# macOS/Linux
lsof -i :8080
netstat -tulpn | grep :8080

# 2. 停止占用端口的容器
docker ps | grep 8080
docker stop <container_name>

# 3. 使用不同的端口映射
docker run -p 8081:80 nginx  # 改用8081端口

# 4. 查找并停止所有使用该端口的容器
docker ps --filter "publish=8080"

# 5. 强制删除占用端口的容器
docker rm -f $(docker ps -q --filter "publish=8080")
```

---

### 🚨 问题3：镜像拉取失败

**错误信息**：
```bash
Error response from daemon: pull access denied for xxx, repository does not exist or may require 'docker login'
```

**解决步骤**：

```bash
# 1. 检查镜像名称是否正确
docker search nginx  # 搜索可用镜像

# 2. 登录Docker Hub（如果是私有镜像）
docker login
# 输入用户名和密码

# 3. 配置镜像加速器（中国用户）
# 编辑 /etc/docker/daemon.json
{
  "registry-mirrors": [
    "https://mirror.ccs.tencentyun.com",
    "https://registry.docker-cn.com"
  ]
}

# 重启Docker服务
sudo systemctl restart docker

# 4. 使用完整的镜像路径
docker pull docker.io/library/nginx:latest

# 5. 检查网络连接
ping registry-1.docker.io
```

---

### 🚨 问题4：容器内无法访问外网

**错误信息**：
```bash
# 在容器内执行
curl: (6) Could not resolve host: google.com
```

**解决步骤**：

```bash
# 1. 检查主机网络连接
ping google.com

# 2. 检查Docker网络配置
docker network ls
docker network inspect bridge

# 3. 重启Docker服务
sudo systemctl restart docker

# 4. 检查DNS配置
docker run --rm alpine nslookup google.com

# 5. 使用自定义DNS
docker run --dns=******* --rm alpine nslookup google.com

# 6. 检查防火墙设置
# Ubuntu/Debian
sudo ufw status
sudo ufw allow 2376/tcp

# CentOS/RHEL
sudo firewall-cmd --list-all
sudo firewall-cmd --add-port=2376/tcp --permanent
sudo firewall-cmd --reload
```

---

### 🚨 问题5：磁盘空间不足

**错误信息**：
```bash
Error response from daemon: no space left on device
```

**解决步骤**：

```bash
# 1. 查看磁盘使用情况
df -h
docker system df

# 2. 清理停止的容器
docker container prune

# 3. 清理未使用的镜像
docker image prune
docker image prune -a  # 清理所有未使用的镜像

# 4. 清理未使用的数据卷
docker volume prune

# 5. 清理未使用的网络
docker network prune

# 6. 一键清理所有未使用的资源
docker system prune -a --volumes

# 7. 查看具体占用空间的镜像
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"

# 8. 移动Docker数据目录（Linux）
sudo systemctl stop docker
sudo mv /var/lib/docker /new/path/docker
sudo ln -s /new/path/docker /var/lib/docker
sudo systemctl start docker
```

---

### 🚨 问题6：容器启动后立即退出

**错误信息**：
```bash
# 容器状态显示为Exited (0) 或 Exited (1)
```

**解决步骤**：

```bash
# 1. 查看容器退出状态
docker ps -a

# 2. 查看容器日志
docker logs <container_name>

# 3. 检查Dockerfile中的CMD或ENTRYPOINT
# 确保有前台运行的进程

# 4. 使用交互模式调试
docker run -it <image> /bin/bash

# 5. 常见问题修复
# 问题：没有前台进程
# 错误的CMD
CMD echo "Hello World"
# 正确的CMD
CMD ["nginx", "-g", "daemon off;"]

# 问题：权限不足
# 检查文件权限
ls -la /app/
chmod +x /app/start.sh

# 6. 使用--restart参数自动重启
docker run --restart=unless-stopped <image>
```

---

### 🚨 问题7：构建镜像时依赖安装失败

**错误信息**：
```bash
E: Unable to locate package xxx
pip: command not found
```

**解决步骤**：

```dockerfile
# 1. 更新包管理器
# Debian/Ubuntu
RUN apt-get update && apt-get install -y package_name

# Alpine
RUN apk update && apk add package_name

# CentOS/RHEL
RUN yum update && yum install -y package_name

# 2. 使用正确的包名
# 查找正确的包名
RUN apt-cache search python3
RUN apk search python3

# 3. 清理缓存减少镜像大小
RUN apt-get update \
    && apt-get install -y python3 python3-pip \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 4. 使用多阶段构建
FROM python:3.11-alpine AS builder
RUN pip install --user package_name

FROM python:3.11-alpine AS runtime
COPY --from=builder /root/.local /root/.local

# 5. 固定版本避免构建不一致
RUN pip install flask==2.3.3
```

---

### 🚨 问题8：容器间无法通信

**错误信息**：
```bash
curl: (7) Failed to connect to web:80: Connection refused
```

**解决步骤**：

```bash
# 1. 检查容器是否在同一网络
docker network ls
docker inspect <container1> | grep NetworkMode
docker inspect <container2> | grep NetworkMode

# 2. 创建自定义网络
docker network create mynetwork
docker run --network=mynetwork --name web nginx
docker run --network=mynetwork --name app alpine

# 3. 使用docker-compose（推荐）
version: '3.8'
services:
  web:
    image: nginx
  app:
    image: alpine
    depends_on:
      - web

# 4. 检查端口是否正确暴露
docker run -p 80:80 nginx  # 映射到主机
docker run --expose 80 nginx  # 只暴露给其他容器

# 5. 使用容器名称而不是IP地址
# 正确
curl http://web:80
# 错误（IP可能变化）
curl http://**********:80

# 6. 检查防火墙规则
docker exec web netstat -tulpn
docker exec app ping web
```

---

### 🚨 问题9：数据卷挂载失败

**错误信息**：
```bash
Error response from daemon: invalid mount config for type "bind": bind source path does not exist
```

**解决步骤**：

```bash
# 1. 检查主机路径是否存在
ls -la /host/path

# 2. 使用绝对路径
# 错误
docker run -v ./data:/app/data nginx
# 正确
docker run -v $(pwd)/data:/app/data nginx
docker run -v /absolute/path/data:/app/data nginx

# 3. 创建目录
mkdir -p /host/path/data
docker run -v /host/path/data:/app/data nginx

# 4. 检查权限
# 确保Docker有权限访问挂载目录
sudo chown -R $USER:$USER /host/path
chmod 755 /host/path

# 5. 使用命名数据卷
docker volume create myvolume
docker run -v myvolume:/app/data nginx

# 6. Windows路径格式
# PowerShell
docker run -v ${PWD}/data:/app/data nginx
# CMD
docker run -v %cd%/data:/app/data nginx

# 7. 检查SELinux（CentOS/RHEL）
# 添加:Z标志
docker run -v /host/path:/app/data:Z nginx
```

---

### 🚨 问题10：内存不足导致容器被杀死

**错误信息**：
```bash
# 容器日志显示
Killed
# 或者系统日志显示
Out of memory: Kill process
```

**解决步骤**：

```bash
# 1. 检查系统内存使用
free -h
docker stats

# 2. 限制容器内存使用
docker run -m 512m nginx  # 限制512MB内存
docker run --memory=1g --memory-swap=2g nginx

# 3. 检查容器内存使用
docker stats <container_name>

# 4. 优化应用程序
# 减少内存使用，优化代码

# 5. 使用更小的基础镜像
FROM python:3.11-alpine  # 而不是 python:3.11

# 6. 清理不必要的文件
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# 7. 增加系统swap空间（Linux）
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# 8. 监控内存使用趋势
docker run --name cadvisor -p 8080:8080 \
  -v /:/rootfs:ro \
  -v /var/run:/var/run:ro \
  -v /sys:/sys:ro \
  -v /var/lib/docker/:/var/lib/docker:ro \
  google/cadvisor:latest
```

---

### 🛠️ 调试技巧和最佳实践

#### 1. 日志调试
```bash
# 查看容器日志
docker logs -f --tail 100 <container_name>

# 查看系统日志
# Linux
journalctl -u docker.service
tail -f /var/log/docker.log

# 设置日志级别
# 编辑 /etc/docker/daemon.json
{
  "log-level": "debug"
}
```

#### 2. 网络调试
```bash
# 检查网络连接
docker exec <container> ping google.com
docker exec <container> nslookup google.com
docker exec <container> netstat -tulpn

# 检查端口映射
docker port <container>

# 抓包分析
docker exec <container> tcpdump -i eth0
```

#### 3. 性能调试
```bash
# 查看资源使用
docker stats
docker exec <container> top
docker exec <container> ps aux

# 查看磁盘使用
docker exec <container> df -h
docker system df

# 查看进程
docker exec <container> ps -ef
```

#### 4. 预防性措施
```bash
# 1. 使用健康检查
HEALTHCHECK --interval=30s --timeout=3s \
  CMD curl -f http://localhost/ || exit 1

# 2. 设置资源限制
docker run --memory=1g --cpus=1.5 nginx

# 3. 使用多阶段构建减少镜像大小
FROM node:16 AS builder
# 构建步骤
FROM node:16-alpine AS runtime
COPY --from=builder /app/dist ./dist

# 4. 定期清理
# 添加到crontab
0 2 * * * docker system prune -f

# 5. 监控和告警
# 使用Prometheus + Grafana监控Docker
```

---

## 7. 学习检查清单

### 📋 第1章：Docker简介与核心概念

#### 理论理解 ✅
- [ ] **容器化概念**：能够解释什么是容器化，以及它与传统虚拟化的区别
- [ ] **Docker架构**：理解Docker Client、Docker Daemon、镜像和容器的关系
- [ ] **镜像层级**：明白镜像的分层存储机制和写时复制原理
- [ ] **仓库系统**：了解Docker Hub、私有仓库的作用和使用场景

#### 实践操作 ✅
- [ ] **环境验证**：成功安装Docker并运行hello-world容器
- [ ] **基本操作**：能够拉取镜像、创建容器、查看状态
- [ ] **概念应用**：能够区分镜像和容器，理解它们的生命周期

**验证方法**：
```bash
# 理论验证：向他人解释Docker的核心概念
# 实践验证：
docker run hello-world
docker images
docker ps -a
```

---

### 📋 第2章：环境安装与配置

#### 理论理解 ✅
- [ ] **系统要求**：了解不同操作系统的Docker安装要求
- [ ] **网络配置**：理解Docker网络模式和端口映射原理
- [ ] **存储配置**：明白数据卷和绑定挂载的区别

#### 实践操作 ✅
- [ ] **成功安装**：在自己的操作系统上成功安装Docker
- [ ] **配置优化**：配置镜像加速器，优化下载速度
- [ ] **权限设置**：正确配置用户权限（Linux用户）

**验证方法**：
```bash
# 检查安装
docker --version
docker info

# 检查配置
docker run -d --name test-nginx -p 8080:80 nginx
curl http://localhost:8080
docker stop test-nginx && docker rm test-nginx
```

---

### 📋 第3章：Docker核心命令详解

#### 理论理解 ✅
- [ ] **命令分类**：理解镜像、容器、网络、数据卷管理命令的分类
- [ ] **参数含义**：掌握常用参数的作用和使用场景
- [ ] **命令组合**：理解如何组合使用多个命令完成复杂任务

#### 实践操作 ✅
- [ ] **镜像操作**：熟练使用pull、images、rmi、tag等命令
- [ ] **容器管理**：掌握run、ps、exec、stop、rm等命令
- [ ] **系统维护**：能够使用logs、stats、system prune等命令

**验证方法**：
```bash
# 完成以下任务序列
docker pull alpine:latest
docker run -it --name my-alpine alpine sh
# 在容器内安装curl并测试
docker commit my-alpine my-alpine:with-curl
docker images | grep my-alpine
docker stop my-alpine && docker rm my-alpine
docker rmi my-alpine:with-curl
```

---

### 📋 第4章：Dockerfile编写指南

#### 理论理解 ✅
- [ ] **指令理解**：掌握FROM、RUN、COPY、CMD等核心指令的作用
- [ ] **最佳实践**：理解镜像优化、安全性、可维护性的原则
- [ ] **多阶段构建**：明白多阶段构建的优势和使用场景

#### 实践操作 ✅
- [ ] **基础构建**：能够编写简单的Dockerfile并成功构建镜像
- [ ] **优化技巧**：应用镜像优化技巧，减小镜像大小
- [ ] **复杂应用**：构建包含多个组件的应用镜像

**验证方法**：
```bash
# 创建一个优化的Python应用镜像
# 要求：使用alpine基础镜像，多阶段构建，非root用户运行
# 镜像大小应小于100MB
docker build -t my-python-app .
docker images my-python-app
docker run --rm my-python-app python --version
```

---

### 📋 第5章：实践练习项目

#### 理论理解 ✅
- [ ] **项目架构**：理解单容器到多容器应用的架构演进
- [ ] **服务编排**：掌握Docker Compose的配置和使用原理
- [ ] **数据持久化**：理解数据卷在实际项目中的应用

#### 实践操作 ✅
- [ ] **练习1完成**：成功完成基础容器操作练习
- [ ] **练习2完成**：部署Web服务器并实现端口映射和数据挂载
- [ ] **练习3完成**：构建自定义Python应用镜像
- [ ] **练习4完成**：使用Docker Compose部署多容器应用
- [ ] **练习5理解**：了解生产环境部署的基本概念

**验证方法**：
```bash
# 验证练习4的多容器应用
cd fullstack-app
docker-compose up -d
curl http://localhost/api/health
docker-compose ps
docker-compose down
```

---

### 📋 第6章：常见问题与故障排除

#### 理论理解 ✅
- [ ] **问题分类**：能够识别和分类常见的Docker问题
- [ ] **调试思路**：掌握系统性的问题排查方法
- [ ] **预防措施**：了解如何预防常见问题的发生

#### 实践操作 ✅
- [ ] **问题重现**：能够重现文档中提到的常见问题
- [ ] **解决问题**：成功解决至少5个不同类型的问题
- [ ] **调试技能**：掌握使用日志、监控工具进行调试

**验证方法**：
```bash
# 故意制造并解决一个端口冲突问题
docker run -d --name nginx1 -p 8080:80 nginx
docker run -d --name nginx2 -p 8080:80 nginx  # 这会失败
# 解决冲突并验证两个容器都能正常运行
```

---

### 🎯 综合能力评估

#### 初级水平 (Day 1结束)
- [ ] 能够独立安装和配置Docker环境
- [ ] 掌握基本的镜像和容器操作命令
- [ ] 能够运行简单的容器化应用
- [ ] 理解Docker的基本概念和工作原理

#### 中级水平 (Day 2结束)
- [ ] 能够编写Dockerfile构建自定义镜像
- [ ] 掌握Docker Compose进行多容器编排
- [ ] 能够解决常见的Docker问题
- [ ] 理解容器化应用的最佳实践

#### 高级目标 (后续学习)
- [ ] 能够设计和实现完整的容器化解决方案
- [ ] 掌握Kubernetes等容器编排平台
- [ ] 能够构建CI/CD流水线
- [ ] 具备生产环境容器化部署经验

---

## 8. 进阶学习路径

### 🚀 立即行动计划（完成本指南后）

#### 第1周：深化Docker技能
- [ ] **Docker网络深入**：学习自定义网络、网络驱动、跨主机通信
- [ ] **数据管理进阶**：掌握数据卷驱动、备份恢复策略
- [ ] **安全最佳实践**：学习镜像安全扫描、运行时安全、secrets管理

**推荐资源**：
- 📖 [Docker官方网络指南](https://docs.docker.com/network/)
- 📖 [Docker安全最佳实践](https://docs.docker.com/engine/security/)
- 🎥 [Docker深入浅出视频教程](https://www.bilibili.com/video/BV1s54y1n7Ev)

#### 第2周：容器编排入门
- [ ] **Docker Swarm**：学习Docker原生集群管理
- [ ] **Kubernetes基础**：理解Pod、Service、Deployment概念
- [ ] **Helm包管理**：掌握Kubernetes应用包管理工具

**推荐资源**：
- 📖 [Kubernetes官方教程](https://kubernetes.io/docs/tutorials/)
- 📖 [Helm官方文档](https://helm.sh/docs/)
- 🎥 [Kubernetes入门实战](https://www.imooc.com/learn/1266)

#### 第3-4周：CI/CD集成
- [ ] **GitLab CI/CD**：学习基于GitLab的容器化CI/CD
- [ ] **Jenkins Pipeline**：掌握Jenkins容器化构建流水线
- [ ] **GitHub Actions**：实现基于GitHub的自动化部署

**推荐资源**：
- 📖 [GitLab CI/CD文档](https://docs.gitlab.com/ee/ci/)
- 📖 [Jenkins Docker插件](https://plugins.jenkins.io/docker-plugin/)
- 🛠️ [GitHub Actions市场](https://github.com/marketplace?type=actions)

---

### 📚 技术栈扩展路径

#### 路径1：微服务架构师
```
Docker基础 → Kubernetes → Service Mesh(Istio) → 微服务设计模式
```

**学习重点**：
- 服务发现和负载均衡
- 分布式追踪和监控
- API网关和服务治理
- 数据一致性和事务管理

**推荐书籍**：
- 📚 《微服务架构设计模式》- Chris Richardson
- 📚 《Kubernetes权威指南》- 龚正等
- 📚 《云原生应用架构实践》- Gary Stafford

#### 路径2：DevOps工程师
```
Docker基础 → CI/CD → 监控告警 → 基础设施即代码(IaC)
```

**学习重点**：
- 自动化部署和回滚
- 监控、日志和告警系统
- 基础设施自动化管理
- 性能优化和故障排除

**推荐工具**：
- 🛠️ **监控**：Prometheus + Grafana
- 🛠️ **日志**：ELK Stack (Elasticsearch + Logstash + Kibana)
- 🛠️ **IaC**：Terraform + Ansible
- 🛠️ **CI/CD**：Jenkins + GitLab CI + ArgoCD

#### 路径3：云原生开发者
```
Docker基础 → Kubernetes → 云服务集成 → Serverless
```

**学习重点**：
- 云原生应用开发模式
- 容器化应用的云服务集成
- Serverless架构和FaaS
- 多云和混合云策略

**推荐平台**：
- ☁️ **AWS**：EKS + Fargate + Lambda
- ☁️ **Azure**：AKS + Container Instances + Functions
- ☁️ **GCP**：GKE + Cloud Run + Cloud Functions
- ☁️ **阿里云**：ACK + ECI + 函数计算

---

### 🎓 认证和证书

#### Docker相关认证
- 🏆 **Docker Certified Associate (DCA)**
  - 官方认证，验证Docker技能
  - 考试费用：$195
  - 有效期：2年

#### Kubernetes认证
- 🏆 **Certified Kubernetes Administrator (CKA)**
  - CNCF官方认证
  - 考试费用：$375
  - 实操考试，难度较高

- 🏆 **Certified Kubernetes Application Developer (CKAD)**
  - 面向应用开发者
  - 考试费用：$375
  - 专注于应用部署和管理

#### 云厂商认证
- 🏆 **AWS Certified Solutions Architect**
- 🏆 **Azure Solutions Architect Expert**
- 🏆 **Google Cloud Professional Cloud Architect**

---

### 🌟 实践项目建议

#### 初级项目
1. **个人博客容器化**
   - 使用Docker部署WordPress + MySQL
   - 配置Nginx反向代理
   - 实现数据持久化

2. **微服务Demo应用**
   - 构建用户服务、订单服务、商品服务
   - 使用Docker Compose编排
   - 添加API网关

#### 中级项目
1. **完整的CI/CD流水线**
   - 代码提交触发自动构建
   - 自动化测试和安全扫描
   - 多环境自动部署

2. **监控和日志系统**
   - 部署Prometheus + Grafana
   - 配置ELK日志收集
   - 设置告警规则

#### 高级项目
1. **多云Kubernetes集群**
   - 在多个云平台部署K8s集群
   - 实现跨云负载均衡
   - 配置灾难恢复

2. **企业级容器平台**
   - 构建私有容器云平台
   - 实现多租户隔离
   - 集成LDAP认证和RBAC

---

### 📖 推荐学习资源

#### 官方文档
- 🔗 [Docker官方文档](https://docs.docker.com/)
- 🔗 [Kubernetes官方文档](https://kubernetes.io/docs/)
- 🔗 [Docker Hub](https://hub.docker.com/)

#### 在线课程
- 🎓 [Docker深入浅出 - 极客时间](https://time.geekbang.org/course/intro/100015201)
- 🎓 [Kubernetes权威指南 - 极客时间](https://time.geekbang.org/course/intro/100012101)
- 🎓 [Docker and Kubernetes - Udemy](https://www.udemy.com/course/docker-and-kubernetes-the-complete-guide/)

#### 技术博客
- 📝 [Docker官方博客](https://www.docker.com/blog/)
- 📝 [Kubernetes官方博客](https://kubernetes.io/blog/)
- 📝 [CNCF博客](https://www.cncf.io/blog/)

#### 开源项目
- 🔧 [Awesome Docker](https://github.com/veggiemonk/awesome-docker)
- 🔧 [Kubernetes Examples](https://github.com/kubernetes/examples)
- 🔧 [Docker Samples](https://github.com/dockersamples)

#### 社区和论坛
- 💬 [Docker Community](https://www.docker.com/community)
- 💬 [Kubernetes Slack](https://kubernetes.slack.com/)
- 💬 [Reddit r/docker](https://www.reddit.com/r/docker/)
- 💬 [Stack Overflow](https://stackoverflow.com/questions/tagged/docker)

---

### 🎯 学习建议

#### 1. 循序渐进
- 不要急于求成，扎实掌握每个概念
- 理论学习与实践操作相结合
- 定期回顾和总结学过的内容

#### 2. 动手实践
- 每学一个概念都要亲自动手验证
- 搭建实验环境进行测试
- 记录遇到的问题和解决方案

#### 3. 关注社区
- 关注Docker和Kubernetes的官方动态
- 参与开源项目贡献代码
- 分享学习心得和经验

#### 4. 持续学习
- 容器技术发展迅速，保持学习热情
- 关注新技术和最佳实践
- 参加技术会议和培训

---

## 🎉 结语

恭喜您完成了Docker快速入门指南！通过这份指南，您已经：

✅ **掌握了Docker的核心概念和基本操作**
✅ **学会了编写Dockerfile和使用Docker Compose**
✅ **完成了从简单到复杂的实践项目**
✅ **具备了解决常见问题的能力**

### 下一步行动
1. **巩固基础**：重复练习本指南中的实践项目
2. **扩展技能**：按照进阶学习路径继续深入
3. **实际应用**：在工作或个人项目中应用Docker技术
4. **持续学习**：关注容器技术的最新发展

### 保持联系
- 🌟 如果这份指南对您有帮助，请给个Star
- 🐛 发现问题或有改进建议，欢迎提Issue
- 💬 加入Docker技术交流群，与同行交流学习

**记住**：容器化是现代软件开发的重要技能，掌握Docker将为您的技术生涯带来巨大价值。继续加油，成为容器技术专家！

---

*最后更新：2024年1月*
*版本：v1.0*
*作者：Docker学习指南编写组*
```
